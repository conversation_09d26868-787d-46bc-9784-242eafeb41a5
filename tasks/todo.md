# Fix Zoom Flipping and Text Region Border Calculation

## Problem Analysis
The zoom functionality has issues with text region border calculations that interfere with zoom behavior:

1. **Text Region Border Thickness Issue**: Text region borders use fixed strokeWidth values that don't compensate for zoom scaling, causing borders to appear thicker/thinner at different zoom levels
2. **Zoom State Management**: Potential conflicts between EditorContext zoom state and local effective zoom state in CanvasWorkspace
3. **Coordinate Transformation**: Inconsistent zoom values might cause "zoom flipping" behavior

## Todo Items

### Phase 1: Fix Text Region Border Thickness
- [ ] Modify TextRegion component to access zoom level from EditorContext
- [ ] Update strokeWidth calculation to maintain constant visual thickness (divide by zoom)
- [ ] Test border thickness consistency across different zoom levels

### Phase 2: Improve Zoom State Management  
- [ ] Review zoom state usage in CanvasWorkspace to ensure consistency
- [ ] Verify that effectiveZoom and state.zoom are properly synchronized
- [ ] Check coordinate transformation functions use consistent zoom values

### Phase 3: Fix Drawing Tool Border Consistency
- [ ] Update TextRegionDrawTool to use zoom-adjusted stroke width
- [ ] Ensure drawing preview borders maintain consistent thickness

### Phase 4: Testing and Validation
- [ ] Test zoom in/out behavior with text regions
- [ ] Verify no "zoom flipping" occurs during interaction
- [ ] Confirm border thickness remains visually consistent at all zoom levels
- [ ] Test text region selection and editing at various zoom levels

## Implementation Details

### Text Region Border Fix
- Access zoom from EditorContext using useEditor hook
- Calculate strokeWidth as: `baseStrokeWidth / zoom`
- Base stroke widths: selected=3, hovered=2.5, normal=2

### Zoom Consistency
- Ensure all coordinate transformations use the same zoom value
- Verify stage scaling and coordinate calculations are synchronized

## Files to Modify
- `packages/frontend/components/editor/TextRegion.tsx` - Main border fix
- `packages/frontend/components/editor/TextRegionDrawTool.tsx` - Drawing tool borders
- `packages/frontend/components/editor/CanvasWorkspace.tsx` - Zoom state review (if needed)
