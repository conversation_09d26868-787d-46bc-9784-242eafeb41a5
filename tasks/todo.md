# Fix Zoom Flipping and Text Region Border Calculation

## Problem Analysis
The zoom functionality has issues with text region border calculations that interfere with zoom behavior:

1. **Text Region Border Thickness Issue**: Text region borders use fixed strokeWidth values that don't compensate for zoom scaling, causing borders to appear thicker/thinner at different zoom levels
2. **Zoom State Management**: Potential conflicts between EditorContext zoom state and local effective zoom state in CanvasWorkspace
3. **Coordinate Transformation**: Inconsistent zoom values might cause "zoom flipping" behavior

## Todo Items

### Phase 1: Fix Text Region Border Thickness
- [x] Modify TextRegion component to access zoom level from EditorContext
- [x] Update strokeWidth calculation to maintain constant visual thickness (divide by zoom)
- [x] Test border thickness consistency across different zoom levels

### Phase 2: Improve Zoom State Management
- [x] Review zoom state usage in CanvasWorkspace to ensure consistency
- [x] Verify that effectiveZoom and state.zoom are properly synchronized
- [x] Check coordinate transformation functions use consistent zoom values

### Phase 3: Fix Drawing Tool Border Consistency
- [x] Update TextRegionDrawTool to use zoom-adjusted stroke width
- [x] Ensure drawing preview borders maintain consistent thickness

### Phase 4: Testing and Validation
- [/] Test zoom in/out behavior with text regions
- [/] Verify no "zoom flipping" occurs during interaction
- [/] Confirm border thickness remains visually consistent at all zoom levels
- [/] Test text region selection and editing at various zoom levels

## Implementation Details

### Text Region Border Fix
- Access zoom from EditorContext using useEditor hook
- Calculate strokeWidth as: `baseStrokeWidth / zoom`
- Base stroke widths: selected=3, hovered=2.5, normal=2

### Zoom Consistency
- Ensure all coordinate transformations use the same zoom value
- Verify stage scaling and coordinate calculations are synchronized

## Files to Modify
- `packages/frontend/components/editor/TextRegion.tsx` - Main border fix
- `packages/frontend/components/editor/TextRegionDrawTool.tsx` - Drawing tool borders
- `packages/frontend/components/editor/CanvasWorkspace.tsx` - Zoom state review (if needed)

## Review

### Changes Made

#### 1. TextRegion Border Thickness Fix
- **File**: `packages/frontend/components/editor/TextRegion.tsx`
- **Changes**:
  - Added `zoom: number` prop to TextRegionProps interface
  - Updated component function signature to accept zoom prop
  - Modified strokeWidth calculation: `baseStrokeWidth / Math.max(zoom, 0.1)`
  - Base stroke widths remain: selected=3, hovered=2.5, normal=2

#### 2. TextRegionDrawTool Border Consistency
- **File**: `packages/frontend/components/editor/TextRegionDrawTool.tsx`
- **Changes**:
  - Added `zoom: number` prop to TextRegionDrawToolProps interface
  - Updated component function signature to accept zoom prop
  - Added zoom-adjusted strokeWidth calculation: `2 / Math.max(zoom, 0.1)`
  - Applied strokeWidth to the drawing preview Rect component

#### 3. Zoom State Consistency
- **File**: `packages/frontend/components/editor/CanvasWorkspace.tsx`
- **Changes**:
  - Updated TextRegion components to receive `zoom={effectiveZoom}` prop
  - Updated TextRegionDrawTool to receive `zoom={effectiveZoom}` prop
  - Ensured consistent use of `effectiveZoom` for all visual elements

### Key Improvements

1. **Consistent Border Thickness**: Text region borders now maintain constant visual thickness regardless of zoom level
2. **Zoom State Consistency**: All components now use `effectiveZoom` for visual calculations, eliminating potential conflicts
3. **Drawing Tool Consistency**: Drawing preview borders also maintain consistent thickness
4. **Type Safety**: All changes maintain TypeScript type safety with proper prop interfaces

### Technical Details

- **Border Calculation**: `strokeWidth = baseStrokeWidth / zoom` ensures visual consistency
- **Zoom Source**: Using `effectiveZoom` (which includes preview state) instead of `state.zoom` for real-time responsiveness
- **Safety**: Added `Math.max(zoom, 0.1)` to prevent division by zero errors
- **Performance**: No performance impact as calculations are simple divisions

### Expected Results

- Text region borders should appear the same thickness at all zoom levels
- Drawing tool preview borders should also maintain consistent thickness
- No more "zoom flipping" behavior during zoom operations
- Smooth zoom interactions without visual artifacts

### Testing Recommendations

1. Test zooming in/out with text regions visible
2. Verify border thickness remains visually consistent
3. Test drawing new text regions at various zoom levels
4. Confirm no visual glitches during zoom operations
5. Test text region selection and editing at different zoom levels
