'use client';

import { useRef, useEffect, useCallback, useState } from 'react';
import dynamic from 'next/dynamic';
import { Stage, Layer, Image as KonvaImage, Rect, Text } from 'react-konva';
import { useEditor } from './EditorContext';
import { useImageLoader } from './hooks/useImageLoader';
import { TextRegionResponse, TextRegionType, TranslationStatus } from '@/types/api';
import ImageUploader from './ImageUploader';

// Dynamically import Konva-dependent components
const TextRegion = dynamic(() => import('./TextRegion'), { ssr: false });
const TextRegionDrawTool = dynamic(() => import('./TextRegionDrawTool'), { ssr: false });

export default function CanvasWorkspace() {
  const { state, dispatch } = useEditor();
  const stageRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isClient, setIsClient] = useState(false);

  // Preview state for viewport operations
  const [previewViewport, setPreviewViewport] = useState<{
    zoom?: number;
    offset?: { x: number; y: number };
  }>({});
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const resizeTimeout = useRef<NodeJS.Timeout | null>(null);
  const isInteracting = useRef(false);

  // Temporary panning state
  const [isTemporaryPanning, setIsTemporaryPanning] = useState(false);
  const previousToolRef = useRef<string | null>(null);

  // Text region drawing state
  const [textRegionDrawing, setTextRegionDrawing] = useState<{
    isDrawing: boolean;
    startX: number;
    startY: number;
    currentX: number;
    currentY: number;
  }>({
    isDrawing: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });

  // Text region drawing preview state for RAF optimization
  const [drawingPreview, setDrawingPreview] = useState<{
    currentX: number;
    currentY: number;
  } | null>(null);
  const drawingRafId = useRef<number | null>(null);
  const latestDrawingCoords = useRef<{ x: number; y: number } | null>(null);

  // Sample manga page image (you can replace this with actual page data)
  const [sampleImageSrc, setSampleImageSrc] = useState<string | undefined>(undefined);
  const { image: mangaImage, isLoading: imageLoading, error: imageError } = useImageLoader(sampleImageSrc);

  // Text region drawing helper functions
  const transformPointerCoordinates = useCallback((pointer: { x: number; y: number }) => {
    return {
      x: (pointer.x - state.viewportOffset.x) / state.zoom,
      y: (pointer.y - state.viewportOffset.y) / state.zoom
    };
  }, [state.viewportOffset, state.zoom]);

  // RAF-optimized drawing preview update
  const updateDrawingPreview = useCallback(() => {
    if (latestDrawingCoords.current) {
      setDrawingPreview({
        currentX: latestDrawingCoords.current.x,
        currentY: latestDrawingCoords.current.y
      });
      latestDrawingCoords.current = null;
    }
    drawingRafId.current = null;
  }, []);

  // Start text region drawing
  const startTextRegionDrawing = useCallback((pointer: { x: number; y: number }) => {
    const transformed = transformPointerCoordinates(pointer);
    setTextRegionDrawing({
      isDrawing: true,
      startX: transformed.x,
      startY: transformed.y,
      currentX: transformed.x,
      currentY: transformed.y,
    });
    setDrawingPreview({
      currentX: transformed.x,
      currentY: transformed.y
    });
  }, [transformPointerCoordinates]);

  // Update text region drawing
  const updateTextRegionDrawing = useCallback((pointer: { x: number; y: number }) => {
    if (!textRegionDrawing.isDrawing) return;

    const transformed = transformPointerCoordinates(pointer);
    latestDrawingCoords.current = transformed;

    // Schedule RAF update if not already scheduled
    if (drawingRafId.current === null) {
      drawingRafId.current = requestAnimationFrame(updateDrawingPreview);
    }
  }, [textRegionDrawing.isDrawing, transformPointerCoordinates, updateDrawingPreview]);

  // Finish text region drawing and create region
  const finishTextRegionDrawing = useCallback(() => {
    if (!textRegionDrawing.isDrawing) return;

    // Cancel any pending RAF updates
    if (drawingRafId.current !== null) {
      cancelAnimationFrame(drawingRafId.current);
      drawingRafId.current = null;
    }

    // Use preview coordinates if available, otherwise use drawing state
    const finalX = drawingPreview?.currentX ?? textRegionDrawing.currentX;
    const finalY = drawingPreview?.currentY ?? textRegionDrawing.currentY;

    const width = Math.abs(finalX - textRegionDrawing.startX);
    const height = Math.abs(finalY - textRegionDrawing.startY);

    // Only create region if it's large enough
    if (width > 10 && height > 10) {
      const x = Math.min(textRegionDrawing.startX, finalX);
      const y = Math.min(textRegionDrawing.startY, finalY);

      // Create new text region (using same logic as TextRegionDrawTool)
      const newRegion: TextRegionResponse = {
        id: `temp-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        page_id: state.currentPage?.id || '',
        region_type: TextRegionType.SPEECH_BUBBLE,
        x,
        y,
        width,
        height,
        original_text: '',
        confidence_score: 1.0,
        translated_text: '',
        translation_status: TranslationStatus.PENDING,
        font_family: 'Arial',
        font_size: 14,
        font_color: '#000000',
        background_color: 'transparent',
        background_opacity: 1.0,
      };

      dispatch({ type: 'ADD_TEXT_REGION', payload: newRegion });
      dispatch({ type: 'SET_SELECTED_REGIONS', payload: [newRegion.id] });
      dispatch({ type: 'SET_SELECTED_TOOL', payload: 'select' });

      // Auto-focus the newly created region for editing
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('auto-edit-region', { detail: { regionId: newRegion.id } }));
      }, 100);
    }

    // Clean up drawing state
    setTextRegionDrawing({
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    });
    setDrawingPreview(null);
    latestDrawingCoords.current = null;
  }, [textRegionDrawing, drawingPreview, state.currentPage, dispatch]);

  // Commit viewport changes to React state with debouncing
  const commitViewportChanges = useCallback(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      // Capture current preview state to avoid race conditions
      setPreviewViewport(currentPreview => {
        if (currentPreview.zoom !== undefined) {
          dispatch({ type: 'SET_ZOOM', payload: currentPreview.zoom });
        }
        if (currentPreview.offset) {
          dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: currentPreview.offset });
        }
        isInteracting.current = false;
        return {}; // Clear preview state
      });
    }, 100); // Reduced debounce time for more responsive feel
  }, [dispatch]);

  // Get effective viewport values (preview or actual state)
  const effectiveZoom = previewViewport.zoom !== undefined ? previewViewport.zoom : state.zoom;
  const effectiveOffset = previewViewport.offset || state.viewportOffset;

  // Check if Option/Alt key is pressed for panning
  const isPanningWithModifier = useCallback((e: any) => {
    return e.evt && (e.evt.altKey || e.evt.metaKey); // Alt on Windows/Linux, Option on Mac
  }, []);

  // Handle temporary panning activation
  const activateTemporaryPanning = useCallback(() => {
    if (!isTemporaryPanning && state.selectedTool !== 'pan') {
      previousToolRef.current = state.selectedTool;
      setIsTemporaryPanning(true);
      dispatch({ type: 'SET_SELECTED_TOOL', payload: 'pan' });
    }
  }, [isTemporaryPanning, state.selectedTool, dispatch]);

  // Handle temporary panning deactivation
  const deactivateTemporaryPanning = useCallback(() => {
    if (isTemporaryPanning && previousToolRef.current) {
      setIsTemporaryPanning(false);
      dispatch({ type: 'SET_SELECTED_TOOL', payload: previousToolRef.current as any });
      previousToolRef.current = null;
    }
  }, [isTemporaryPanning, dispatch]);



  // Load a sample image for demonstration
  const loadSampleImage = () => {
    // Using a placeholder image service for demonstration
    setSampleImageSrc('https://picsum.photos/400/600?random=1');
  };

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current);
      }
    };
  }, []);

  // Handle container resize with ResizeObserver and debouncing
  useEffect(() => {
    if (!isClient || !containerRef.current) return;

    const updateCanvasSize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        dispatch({
          type: 'SET_CANVAS_SIZE',
          payload: { width: Math.floor(width), height: Math.floor(height) }
        });
      }
    };

    const debouncedUpdateCanvasSize = () => {
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current);
      }
      resizeTimeout.current = setTimeout(updateCanvasSize, 100); // 100ms debounce for resize
    };

    // Initial size update (immediate)
    updateCanvasSize();

    // Use ResizeObserver for more accurate resize detection with debouncing
    const resizeObserver = new ResizeObserver(debouncedUpdateCanvasSize);

    resizeObserver.observe(containerRef.current);

    // Fallback to window resize event with debouncing
    window.addEventListener('resize', debouncedUpdateCanvasSize);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', debouncedUpdateCanvasSize);
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current);
      }
    };
  }, [dispatch, isClient]);

  // Handle wheel zoom with smooth scaling using preview approach
  const handleWheel = useCallback((e: any) => {
    e.evt.preventDefault();

    const stage = stageRef.current;
    if (!stage) return;

    isInteracting.current = true;

    // Use current effective zoom for calculations
    const oldScale = effectiveZoom;
    const pointer = stage.getPointerPosition();

    const scaleBy = 1.1;
    const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;

    // Clamp zoom between 0.1x and 5x and round to avoid floating point precision issues
    let clampedScale = Math.round(Math.max(0.1, Math.min(5, newScale)) * 10000) / 10000;

    // Snap to common zoom levels for better UX
    const snapThreshold = 0.02; // 2% threshold for snapping
    const commonZoomLevels = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    for (const level of commonZoomLevels) {
      if (Math.abs(clampedScale - level) < snapThreshold) {
        clampedScale = level;
        break;
      }
    }

    // Adjust position to zoom towards mouse pointer
    const currentOffset = effectiveOffset;
    const mousePointTo = {
      x: (pointer.x - currentOffset.x) / oldScale,
      y: (pointer.y - currentOffset.y) / oldScale,
    };

    const newPos = {
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    };

    // Update preview state instead of dispatching immediately
    setPreviewViewport({
      zoom: clampedScale,
      offset: newPos
    });

    // Commit changes with debouncing
    commitViewportChanges();
  }, [effectiveZoom, effectiveOffset, commitViewportChanges]);

  // Handle stage drag (pan and text region drawing) using preview approach
  const handleStageDrag = useCallback((e: any) => {
    // Priority 0: If we're drawing text regions, prevent stage panning entirely
    if (textRegionDrawing.isDrawing && state.selectedTool === 'text-region') {
      // Prevent the stage from actually moving by resetting its position
      const stage = e.target.getStage();
      stage.position(effectiveOffset);

      // Handle text region drawing updates
      const pointer = stage.getPointerPosition();
      updateTextRegionDrawing(pointer);
      return;
    }

    // Priority checks for different interactions
    const isModifierPanning = isPanningWithModifier(e);
    const isPanToolSelected = state.selectedTool === 'pan';
    const isStageTarget = e.target === e.target.getStage();
    const isBackgroundTarget = e.target.attrs && e.target.attrs.fill === 'white' && !e.target.attrs.text;

    // Handle temporary panning activation/deactivation during drag
    if (isModifierPanning && !isTemporaryPanning && state.selectedTool !== 'pan') {
      activateTemporaryPanning();
    } else if (!isModifierPanning && isTemporaryPanning) {
      deactivateTemporaryPanning();
    }

    // Priority 1: Handle panning (highest priority)
    const shouldPan = isPanToolSelected || isModifierPanning ||
      ((isStageTarget || isBackgroundTarget) && state.selectedTool !== 'text-region');

    if (shouldPan) {
      isInteracting.current = true;

      // Always get the stage position, not the target position
      const stage = e.target.getStage();
      const newOffset = { x: stage.x(), y: stage.y() };

      // Update preview state instead of dispatching immediately
      setPreviewViewport(prev => ({
        ...prev,
        offset: newOffset
      }));
      return;
    }


  }, [state.selectedTool, isPanningWithModifier, isTemporaryPanning, activateTemporaryPanning, deactivateTemporaryPanning, textRegionDrawing.isDrawing, updateTextRegionDrawing]);

  // Handle stage drag end to commit changes
  const handleStageDragEnd = useCallback((e: any) => {
    // Allow panning when:
    // 1. Pan tool is selected (should work regardless of what's under cursor)
    // 2. Modifier key (Alt/Option) is pressed for temporary panning
    // 3. Dragging the stage itself or background
    const isModifierPanning = isPanningWithModifier(e);
    const isPanToolSelected = state.selectedTool === 'pan';
    const isStageTarget = e.target === e.target.getStage();
    const isBackgroundTarget = e.target.attrs && e.target.attrs.fill === 'white' && !e.target.attrs.text;

    // Deactivate temporary panning if modifier is not pressed
    if (!isModifierPanning && isTemporaryPanning) {
      deactivateTemporaryPanning();
    }

    if (!isPanToolSelected && !isStageTarget && !isBackgroundTarget && !isModifierPanning) {
      return;
    }

    // Always get the stage position, not the target position
    const stage = e.target.getStage();
    const newOffset = { x: stage.x(), y: stage.y() };

    // Update preview state and commit
    setPreviewViewport(prev => ({
      ...prev,
      offset: newOffset
    }));

    commitViewportChanges();
  }, [commitViewportChanges, state.selectedTool, isPanningWithModifier, isTemporaryPanning, deactivateTemporaryPanning]);

  // Handle stage click (deselect regions when clicking empty space)
  const handleStageClick = useCallback((e: any) => {
    // Check if clicked on empty area (stage itself or the white paper background)
    const clickedOnStage = e.target === e.target.getStage();
    const clickedOnBackground = e.target.attrs && e.target.attrs.fill === 'white' && !e.target.attrs.text;

    if (clickedOnStage || clickedOnBackground) {
      dispatch({ type: 'SET_SELECTED_REGIONS', payload: [] });

      // Also trigger a custom event to notify text editors to close
      window.dispatchEvent(new CustomEvent('canvas-click-outside'));
    }
  }, [dispatch]);

  // Handle mouse down for potential panning and text region drawing
  const handleMouseDown = useCallback((e: any) => {
    // Priority 1: Check if modifier key is pressed for temporary panning
    if (isPanningWithModifier(e)) {
      activateTemporaryPanning();
      return;
    }

    // Priority 2: Check if we should start text region drawing
    if (state.selectedTool === 'text-region') {
      // Only start drawing if clicking on stage or background (not on existing regions)
      const isStageTarget = e.target === e.target.getStage();
      const isBackgroundTarget = e.target.attrs && e.target.attrs.fill === 'white' && !e.target.attrs.text;

      if (isStageTarget || isBackgroundTarget) {
        const stage = e.target.getStage();
        const pointer = stage.getPointerPosition();
        startTextRegionDrawing(pointer);
        return;
      }
    }
  }, [isPanningWithModifier, activateTemporaryPanning, state.selectedTool, startTextRegionDrawing]);

  // Handle mouse up
  const handleMouseUp = useCallback((e: any) => {
    // Priority 1: Check if we should deactivate temporary panning
    if (isTemporaryPanning && !isPanningWithModifier(e)) {
      deactivateTemporaryPanning();
    }

    // Priority 2: Check if we should finish text region drawing
    if (textRegionDrawing.isDrawing && state.selectedTool === 'text-region') {
      finishTextRegionDrawing();
    }
  }, [isTemporaryPanning, isPanningWithModifier, deactivateTemporaryPanning, textRegionDrawing.isDrawing, state.selectedTool, finishTextRegionDrawing]);

  // Show loading state until client-side is ready
  if (!isClient) {
    return (
      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Initializing Canvas...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="w-full h-full bg-gray-100 relative overflow-hidden"
      style={{ minHeight: '100%', minWidth: '100%' }}
    >
      {/* Canvas Grid Background */}
      {state.showGrid && (
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(to right, #000 1px, transparent 1px),
              linear-gradient(to bottom, #000 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        />
      )}

      {/* Konva Stage */}
      <div data-canvas-container className="relative w-full h-full">
        <Stage
          ref={stageRef}
          width={state.canvasSize.width}
          height={state.canvasSize.height}
          scaleX={effectiveZoom}
          scaleY={effectiveZoom}
          x={effectiveOffset.x}
          y={effectiveOffset.y}
          onWheel={handleWheel}
          onDragMove={handleStageDrag}
          onDragEnd={handleStageDragEnd}
          onClick={handleStageClick}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          draggable={true}
        >
          <Layer>
            {/* Manga Page Background/Placeholder */}
            {!mangaImage && (
              <Rect
                x={50}
                y={50}
                width={400}
                height={600}
                fill="white"
                stroke="#e5e7eb"
                strokeWidth={1}
                shadowColor="black"
                shadowBlur={10}
                shadowOpacity={0.1}
                shadowOffsetX={2}
                shadowOffsetY={2}
              />
            )}

            {/* Manga Page Image */}
            {mangaImage && (
              <KonvaImage
                image={mangaImage}
                x={50}
                y={50}
                width={400}
                height={600}
                listening={false}
              />
            )}

            {/* Loading indicator */}
            {imageLoading && (
              <Text
                x={200}
                y={300}
                text="Loading image..."
                fontSize={16}
                fill="#6b7280"
                align="center"
                listening={false}
              />
            )}

            {/* Error indicator */}
            {imageError && (
              <Text
                x={200}
                y={300}
                text={`Error: ${imageError}`}
                fontSize={16}
                fill="#ef4444"
                align="center"
                listening={false}
              />
            )}

            {/* Text Region Draw Tool */}
            <TextRegionDrawTool
              drawingState={textRegionDrawing}
              drawingPreview={drawingPreview}
              zoom={effectiveZoom}
            />

            {/* Text Regions */}
            {state.textRegions.map((region) => (
              <TextRegion
                key={`${region.id}-${region.background_color}-${region.updated_at}`}
                region={region}
                isSelected={state.selectedRegions.includes(region.id)}
                zoom={effectiveZoom}
                onSelect={(e) => {
                  const isShiftPressed = e?.evt?.shiftKey || false;

                  if (isShiftPressed) {
                    // Shift+click: toggle selection (multiple selection)
                    if (state.selectedRegions.includes(region.id)) {
                      dispatch({
                        type: 'REMOVE_SELECTED_REGION',
                        payload: region.id
                      });
                    } else {
                      dispatch({
                        type: 'ADD_SELECTED_REGION',
                        payload: region.id
                      });
                    }
                  } else {
                    // Regular click: replace selection (single selection)
                    dispatch({
                      type: 'SET_SELECTED_REGIONS',
                      payload: [region.id]
                    });
                  }
                }}
                onTransform={(updates) => {
                  dispatch({
                    type: 'UPDATE_TEXT_REGION',
                    payload: { id: region.id, updates }
                  });
                }}
              />
            ))}
          </Layer>
        </Stage>

        {/* Canvas Overlay UI */}
        <div className="absolute top-4 right-4 space-y-2">
          {/* Zoom Info */}
          <div className="bg-white rounded-lg shadow-lg p-3">
            <div className="text-sm text-gray-600 font-medium">
              Zoom: {Math.round(effectiveZoom * 100)}%
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Tool: {state.selectedTool}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-lg p-2">
            <div className="flex flex-col space-y-1">
              <button
                onClick={() => dispatch({ type: 'SET_ZOOM', payload: 1 })}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              >
                Reset Zoom
              </button>
              <button
                onClick={() => dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: { x: 0, y: 0 } })}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              >
                Center View
              </button>
              <button
                onClick={loadSampleImage}
                className="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors"
              >
                Load Sample
              </button>
              <ImageUploader onImageLoad={setSampleImageSrc} />
            </div>
          </div>
        </div>

        {/* Selection Info */}
        {state.selectedRegions.length > 0 && (
          <div className="absolute bottom-4 left-4 bg-blue-600 text-white rounded-lg shadow-lg p-3">
            <div className="text-sm font-medium">
              {state.selectedRegions.length} region{state.selectedRegions.length !== 1 ? 's' : ''} selected
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
