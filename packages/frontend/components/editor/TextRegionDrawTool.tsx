'use client';

import { Rect } from 'react-konva';

interface DrawingState {
  isDrawing: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

interface DrawingPreview {
  currentX: number;
  currentY: number;
}

interface TextRegionDrawToolProps {
  drawingState: DrawingState;
  drawingPreview: DrawingPreview | null;
  zoom: number;
}

export default function TextRegionDrawTool({ drawingState, drawingPreview, zoom }: TextRegionDrawToolProps) {
  // Calculate drawing rectangle dimensions using preview coordinates when available
  const currentX = drawingPreview?.currentX ?? drawingState.currentX;
  const currentY = drawingPreview?.currentY ?? drawingState.currentY;

  const drawingRect = {
    x: Math.min(drawingState.startX, currentX),
    y: Math.min(drawingState.startY, currentY),
    width: Math.abs(currentX - drawingState.startX),
    height: Math.abs(currentY - drawingState.startY),
  };

  // Calculate stroke width that maintains constant visual thickness regardless of zoom
  const strokeWidth = 2 / Math.max(zoom, 0.1);

  return (
    <>
      {/* Drawing preview rectangle - overlay removed to fix panning conflict */}
      {drawingState.isDrawing && drawingRect.width > 0 && drawingRect.height > 0 && (
        <Rect
          x={drawingRect.x}
          y={drawingRect.y}
          width={drawingRect.width}
          height={drawingRect.height}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth={strokeWidth}
          dash={[5, 5]}
          listening={false}
        />
      )}
    </>
  );
}
