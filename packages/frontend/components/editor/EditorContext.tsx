'use client';

import { createContext, useContext, useReducer, ReactNode } from 'react';
import { ProjectResponse, ProjectPageResponse, TextRegionResponse, TextRegionType, TranslationStatus } from '@/types/api';

// Editor State Types
export interface EditorState {
  // Current project and page
  currentProject: ProjectResponse | null;
  currentPage: ProjectPageResponse | null;
  pages: ProjectPageResponse[];

  // Canvas state
  zoom: number;
  canvasSize: { width: number; height: number };
  viewportOffset: { x: number; y: number };

  // Tools and selection
  selectedTool: 'select' | 'text-region' | 'pan' | 'zoom';
  selectedRegions: string[]; // text region IDs

  // Text regions
  textRegions: TextRegionResponse[];

  // UI state
  showGrid: boolean;
  showRulers: boolean;
  sidebarWidth: number;
  rightPanelWidth: number;
}

// Editor Actions
export type EditorAction =
  | { type: 'SET_PROJECT'; payload: ProjectResponse }
  | { type: 'SET_CURRENT_PAGE'; payload: ProjectPageResponse }
  | { type: 'SET_PAGES'; payload: ProjectPageResponse[] }
  | { type: 'SET_ZOOM'; payload: number }
  | { type: 'SET_CANVAS_SIZE'; payload: { width: number; height: number } }
  | { type: 'SET_VIEWPORT_OFFSET'; payload: { x: number; y: number } }
  | { type: 'SET_SELECTED_TOOL'; payload: EditorState['selectedTool'] }
  | { type: 'SET_SELECTED_REGIONS'; payload: string[] }
  | { type: 'ADD_SELECTED_REGION'; payload: string }
  | { type: 'REMOVE_SELECTED_REGION'; payload: string }
  | { type: 'SET_TEXT_REGIONS'; payload: TextRegionResponse[] }
  | { type: 'ADD_TEXT_REGION'; payload: TextRegionResponse }
  | { type: 'UPDATE_TEXT_REGION'; payload: { id: string; updates: Partial<TextRegionResponse> } }
  | { type: 'UPDATE_TEXT_REGION_STYLES'; payload: { regionIds: string[]; styles: Partial<Pick<TextRegionResponse, 'font_family' | 'font_size' | 'font_color' | 'background_color' | 'background_opacity'>> } }
  | { type: 'REMOVE_TEXT_REGION'; payload: string }
  | { type: 'TOGGLE_GRID' }
  | { type: 'TOGGLE_RULERS' }
  | { type: 'SET_SIDEBAR_WIDTH'; payload: number }
  | { type: 'SET_RIGHT_PANEL_WIDTH'; payload: number };

// Sample text regions for demonstration
const sampleTextRegions: TextRegionResponse[] = [
  {
    id: 'sample-1',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    page_id: 'sample-page',
    region_type: TextRegionType.SPEECH_BUBBLE,
    x: 100,
    y: 100,
    width: 150,
    height: 50,
    original_text: 'こんにちは！',
    confidence_score: 0.95,
    translated_text: 'Hello!',
    translation_status: TranslationStatus.COMPLETED,
    font_family: 'Arial',
    font_size: 14,
    font_color: '#000000',
    background_color: 'transparent',
    background_opacity: 1.0,
  },
  {
    id: 'sample-2',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    page_id: 'sample-page',
    region_type: TextRegionType.NARRATION,
    x: 200,
    y: 200,
    width: 120,
    height: 40,
    original_text: '物語が始まる',
    confidence_score: 0.88,
    translated_text: 'The story begins',
    translation_status: TranslationStatus.COMPLETED,
    font_family: 'Arial',
    font_size: 12,
    font_color: '#374151',
    background_color: 'transparent',
    background_opacity: 1.0,
  },
];

// Initial state
const initialState: EditorState = {
  currentProject: null,
  currentPage: null,
  pages: [],
  zoom: 1,
  canvasSize: { width: 1200, height: 800 }, // Larger initial size, will be updated by resize observer
  viewportOffset: { x: 0, y: 0 },
  selectedTool: 'select',
  selectedRegions: [],
  textRegions: sampleTextRegions,
  showGrid: false,
  showRulers: false,
  sidebarWidth: 300,
  rightPanelWidth: 300,
};

// Reducer
function editorReducer(state: EditorState, action: EditorAction): EditorState {
  switch (action.type) {
    case 'SET_PROJECT':
      return { ...state, currentProject: action.payload };

    case 'SET_CURRENT_PAGE':
      return { ...state, currentPage: action.payload };

    case 'SET_PAGES':
      return { ...state, pages: action.payload };

    case 'SET_ZOOM':
      return { ...state, zoom: action.payload };

    case 'SET_CANVAS_SIZE':
      return { ...state, canvasSize: action.payload };

    case 'SET_VIEWPORT_OFFSET':
      return { ...state, viewportOffset: action.payload };

    case 'SET_SELECTED_TOOL':
      return { ...state, selectedTool: action.payload };

    case 'SET_SELECTED_REGIONS':
      return { ...state, selectedRegions: action.payload };

    case 'ADD_SELECTED_REGION':
      return {
        ...state,
        selectedRegions: [...state.selectedRegions, action.payload]
      };

    case 'REMOVE_SELECTED_REGION':
      return {
        ...state,
        selectedRegions: state.selectedRegions.filter(id => id !== action.payload)
      };

    case 'SET_TEXT_REGIONS':
      return { ...state, textRegions: action.payload };

    case 'ADD_TEXT_REGION':
      return {
        ...state,
        textRegions: [...state.textRegions, action.payload]
      };

    case 'UPDATE_TEXT_REGION':
      return {
        ...state,
        textRegions: state.textRegions.map(region =>
          region.id === action.payload.id
            ? { ...region, ...action.payload.updates }
            : region
        )
      };

    case 'UPDATE_TEXT_REGION_STYLES':
      return {
        ...state,
        textRegions: state.textRegions.map(region =>
          action.payload.regionIds.includes(region.id)
            ? { ...region, ...action.payload.styles }
            : region
        )
      };

    case 'REMOVE_TEXT_REGION':
      return {
        ...state,
        textRegions: state.textRegions.filter(region => region.id !== action.payload),
        selectedRegions: state.selectedRegions.filter(id => id !== action.payload)
      };

    case 'TOGGLE_GRID':
      return { ...state, showGrid: !state.showGrid };

    case 'TOGGLE_RULERS':
      return { ...state, showRulers: !state.showRulers };

    case 'SET_SIDEBAR_WIDTH':
      return { ...state, sidebarWidth: action.payload };

    case 'SET_RIGHT_PANEL_WIDTH':
      return { ...state, rightPanelWidth: action.payload };

    default:
      return state;
  }
}

// Context
const EditorContext = createContext<{
  state: EditorState;
  dispatch: React.Dispatch<EditorAction>;
} | null>(null);

// Provider
export function EditorProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(editorReducer, initialState);

  return (
    <EditorContext.Provider value={{ state, dispatch }}>
      {children}
    </EditorContext.Provider>
  );
}

// Hook
export function useEditor() {
  const context = useContext(EditorContext);
  if (!context) {
    throw new Error('useEditor must be used within an EditorProvider');
  }
  return context;
}
